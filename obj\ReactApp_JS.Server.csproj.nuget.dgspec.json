{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\ReactApp_JS.Server\\ReactApp_JS.Server.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\ReactApp_JS.Server\\ReactApp_JS.Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\ReactApp_JS.Server\\ReactApp_JS.Server.csproj", "projectName": "ReactApp_JS.Server", "projectPath": "C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\ReactApp_JS.Server\\ReactApp_JS.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\ReactApp_JS.Server\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.SpaProxy": {"target": "Package", "version": "[8.*-*, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.100/PortableRuntimeIdentifierGraph.json"}}}}}