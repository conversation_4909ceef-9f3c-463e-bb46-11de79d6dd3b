{"Version": 1, "Hash": "d8cAGSD9FS+DCEkkjFqI786W97cm2CZFJbSIZvRTtoM=", "Source": "ReactApp_JS.Server", "BasePath": "_content/ReactApp_JS.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\reactapp_js.client\\reactapp_js.client.esproj", "Version": 2, "Source": "reactapp_js.client", "GetPublishAssetsTargets": "GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [], "Assets": []}