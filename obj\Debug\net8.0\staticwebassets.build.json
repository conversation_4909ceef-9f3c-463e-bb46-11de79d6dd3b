{"Version": 1, "Hash": "2gMJqs13IIM58QbK7WUYQeVGnULYnStb8iYlccw+jl4=", "Source": "ReactApp_JS.Server", "BasePath": "_content/ReactApp_JS.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\ReactApp_JS\\reactapp_js.client\\reactapp_js.client.esproj", "Version": 2, "Source": "reactapp_js.client", "GetPublishAssetsTargets": "GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [], "Assets": []}