﻿// /src/components/ProductGrid.jsx
import React, { useEffect, useState } from "react";
import axios from "axios";

export default function ProductGrid() {
    const [data, setData] = useState([]);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [useTestData, setUseTestData] = useState(false);
    const [useHttps, setUseHttps] = useState(false);
    const pageSize = 10;

    // Test data for development
    const testData = [
        { productId: 1, productName: 'Test Product 1', genericName: 'Generic 1', therapyArea: 'Cardiology', indication: 'Hypertension' },
        { productId: 2, productName: 'Test Product 2', genericName: 'Generic 2', therapyArea: 'Oncology', indication: 'Cancer' },
        { productId: 3, productName: 'Test Product 3', genericName: 'Generic 3', therapyArea: 'Neurology', indication: 'Epilepsy' }
    ];

    useEffect(() => {
        const fetchData = async () => {
            // If using test data, skip API call
            if (useTestData) {
                setData(testData);
                return;
            }
            debugger;
            setLoading(true);
            setError(null);

            try {
                console.log("Fetching page ${page} with pageSize ${pageSize}");

                // Try HTTP first, then HTTPS if needed
                const protocol = useHttps ? 'https' : 'http';
                const baseUrl = '${protocol}://localhost:7155';
                const response = await axios.get('http://localhost:7155/api/Products', {
                    params: {
                        page,
                        pageSize,
                    },
                    // Add timeout and headers for better error handling
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                console.log("Full API Response:", response);
                console.log("Response data:", response.data);

                // Handle different response structures
                let result;
                if (Array.isArray(response.data)) {
                    result = response.data;
                } else if (response.data && Array.isArray(response.data.data)) {
                    result = response.data.data;
                } else if (response.data && Array.isArray(response.data.items)) {
                    result = response.data.items;
                } else if (response.data && response.data.products) {
                    result = response.data.products;
                } else {
                    console.warn("Unexpected response structure:", response.data);
                    result = [];
                }

                console.log("Processed result:", result);
                setData(result);

            } catch (err) {
                console.error("API fetch error:", err);
                setError(err.message || "Failed to fetch data");

                // More detailed error logging
                if (err.response) {
                    console.error("Error response:", err.response.status, err.response.data);
                } else if (err.request) {
                    console.error("No response received:", err.request);
                } else {
                    console.error("Request setup error:", err.message);
                }
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [page, pageSize, useTestData, useHttps]);

    if (loading) {
        return (
            <div>
                <h2>Product Grid</h2>
                <p>Loading products...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div>
                <h2>Product Grid</h2>
                <p style={{ color: 'red' }}>Error: {error}</p>
                <button onClick={() => window.location.reload()}>Retry</button>
            </div>
        );
    }

    return (
        <div>
            <h2>Product Grid</h2>

            {/* Toggle for test data */}
            <div style={{ marginBottom: '10px' }}>
                <label style={{ marginRight: '20px' }}>
                    <input
                        type="checkbox"
                        checked={useTestData}
                        onChange={(e) => setUseTestData(e.target.checked)}
                    />
                    Use Test Data (for development)
                </label>

                <label>
                    <input
                        type="checkbox"
                        checked={useHttps}
                        onChange={(e) => setUseHttps(e.target.checked)}
                        disabled={useTestData}
                    />
                    Use HTTPS (try if HTTP doesn't work)
                </label>
            </div>

            <p>Total items: {data.length}</p>

            <table border="1" cellPadding="8" cellSpacing="0" style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                    <tr style={{ backgroundColor: '#f5f5f5' }}>
                        <th>Product ID</th>
                        <th>Product Name</th>
                        <th>Generic Name</th>
                        <th>Therapy Area</th>
                        <th>Indication</th>
                    </tr>
                </thead>
                <tbody>
                    {data && data.length > 0 ? (
                        data.map((item, index) => (
                            <tr key={item.productId || item.id || index}>
                                <td>{item.productId || item.id || 'N/A'}</td>
                                <td>{item.productName || item.name || 'N/A'}</td>
                                <td>{item.genericName || item.generic || 'N/A'}</td>
                                <td>{item.therapyArea || item.therapy || 'N/A'}</td>
                                <td>{item.indication || 'N/A'}</td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan="5" style={{ textAlign: 'center', padding: '20px' }}>
                                No data available
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>

            <div style={{ marginTop: "20px", textAlign: 'center' }}>
                <button
                    onClick={() => setPage((p) => Math.max(p - 1, 1))}
                    disabled={page === 1}
                    style={{ marginRight: '10px' }}
                >
                    Previous
                </button>
                <span style={{ margin: "0 15px", fontWeight: 'bold' }}>
                    Page {page}
                </span>
                <button
                    onClick={() => setPage((p) => p + 1)}
                    disabled={data.length < pageSize}
                >
                    Next
                </button>
            </div>
        </div>
    );
}