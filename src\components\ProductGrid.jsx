// /src/components/ProductGrid.jsx
import React, { useEffect, useState } from "react";
import axios from "axios";

export default function ProductGrid() {
    const [data, setData] = useState([]);
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [useTestData, setUseTestData] = useState(true); // Start with test data to verify component works
    const [useHttps, setUseHttps] = useState(false);
    const pageSize = 10;

    // Test data for development
    const testData = [
        { productId: 1, productName: 'Test Product 1', genericName: 'Generic 1', therapyArea: 'Cardiology', indication: 'Hypertension' },
        { productId: 2, productName: 'Test Product 2', genericName: 'Generic 2', therapyArea: 'Oncology', indication: 'Cancer' },
        { productId: 3, productName: 'Test Product 3', genericName: 'Generic 3', therapyArea: 'Neurology', indication: 'Epilepsy' }
    ];

    useEffect(() => {
        const fetchData = async () => {
            // If using test data, skip API call
            if (useTestData) {
                setData(testData);
                return;
            }
            debugger;
            setLoading(true);
            setError(null);

            try {
                console.log(`Fetching page ${page} with pageSize ${pageSize}`);

                // Try HTTP first, then HTTPS if needed
                const protocol = useHttps ? 'https' : 'http';
                const port = useHttps ? '7155' : '5062';
                const baseUrl = `${protocol}://localhost:${port}`;
                const fullUrl = `${baseUrl}/api/Products`;

                console.log(`Making request to: ${fullUrl}`);
                console.log(`Protocol: ${protocol}, Port: ${port}, UseHttps: ${useHttps}`);

                const response = await axios.get(fullUrl, {
                    params: {
                        page,
                        pageSize,
                    },
                    // Add timeout and headers for better error handling
                    timeout: 10000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });

                console.log("Full API Response:", response);
                console.log("Response data:", response.data);

                // Handle different response structures
                let result;
                if (Array.isArray(response.data)) {
                    result = response.data;
                } else if (response.data && Array.isArray(response.data.data)) {
                    result = response.data.data;
                } else if (response.data && Array.isArray(response.data.items)) {
                    result = response.data.items;
                } else if (response.data && response.data.products) {
                    result = response.data.products;
                } else {
                    console.warn("Unexpected response structure:", response.data);
                    result = [];
                }

                console.log("Processed result:", result);
                setData(result);

            } catch (err) {
                console.error("API fetch error:", err);

                // More detailed error logging and user-friendly messages
                if (err.response) {
                    console.error("Error response:", err.response.status, err.response.data);
                    setError(`Server error: ${err.response.status} - ${err.response.statusText}`);
                } else if (err.request) {
                    console.error("No response received:", err.request);
                    setError("Cannot connect to server. Please check if the backend is running.");
                } else {
                    console.error("Request setup error:", err.message);
                    setError(`Request error: ${err.message}`);
                }
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [page, pageSize, useTestData, useHttps]);

    if (loading) {
        return (
            <div>
                <h2>Product Grid</h2>
                <div style={{
                    textAlign: 'center',
                    padding: '40px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '8px',
                    margin: '20px 0'
                }}>
                    <div style={{
                        fontSize: '24px',
                        marginBottom: '16px'
                    }}>
                        ⏳
                    </div>
                    <p style={{
                        fontSize: '18px',
                        color: '#666',
                        margin: '0'
                    }}>
                        Loading products...
                    </p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div>
                <h2>Product Grid</h2>
                <div style={{
                    backgroundColor: '#ffebee',
                    border: '1px solid #f44336',
                    borderRadius: '4px',
                    padding: '16px',
                    margin: '16px 0'
                }}>
                    <p style={{ color: '#d32f2f', margin: '0 0 12px 0', fontWeight: 'bold' }}>
                        ⚠️ Error loading data
                    </p>
                    <p style={{ color: '#d32f2f', margin: '0 0 16px 0' }}>
                        {error}
                    </p>
                    <div>
                        <button
                            onClick={() => window.location.reload()}
                            style={{
                                backgroundColor: '#f44336',
                                color: 'white',
                                border: 'none',
                                padding: '8px 16px',
                                borderRadius: '4px',
                                cursor: 'pointer',
                                marginRight: '8px'
                            }}
                        >
                            Retry
                        </button>
                        <button
                            onClick={() => setUseTestData(true)}
                            style={{
                                backgroundColor: '#2196f3',
                                color: 'white',
                                border: 'none',
                                padding: '8px 16px',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                        >
                            Use Test Data
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div>
            <h2>Product Grid</h2>

            {/* Toggle for test data */}
            <div style={{ marginBottom: '10px' }}>
                <label style={{ marginRight: '20px' }}>
                    <input
                        type="checkbox"
                        checked={useTestData}
                        onChange={(e) => setUseTestData(e.target.checked)}
                    />
                    Use Test Data (for development)
                </label>

                <label style={{ marginRight: '20px' }}>
                    <input
                        type="checkbox"
                        checked={useHttps}
                        onChange={(e) => setUseHttps(e.target.checked)}
                        disabled={useTestData}
                    />
                    Use HTTPS (try if HTTP doesn't work)
                </label>

                <button
                    onClick={async () => {
                        const protocol = useHttps ? 'https' : 'http';
                        const port = useHttps ? '7155' : '5062';
                        const testUrl = `${protocol}://localhost:${port}/api/Products`;
                        console.log(`Testing connection to: ${testUrl}`);
                        try {
                            const response = await fetch(testUrl);
                            console.log('Test response:', response.status, response.statusText);
                            const data = await response.json();
                            console.log('Test data:', data);
                            alert(`✅ Connection successful! Got ${data.length} products`);
                        } catch (err) {
                            console.error('Test connection failed:', err);
                            alert(`❌ Connection failed: ${err.message}`);
                        }
                    }}
                    style={{
                        backgroundColor: '#4caf50',
                        color: 'white',
                        border: 'none',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                    }}
                >
                    Test API Connection
                </button>
            </div>

            <p>Total items: {data.length}</p>

            <table border="1" cellPadding="8" cellSpacing="0" style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                    <tr style={{ backgroundColor: '#f5f5f5' }}>
                        <th>Product ID</th>
                        <th>Product Name</th>
                        <th>Generic Name</th>
                        <th>Therapy Area</th>
                        <th>Indication</th>
                    </tr>
                </thead>
                <tbody>
                    {data && data.length > 0 ? (
                        data.map((item, index) => (
                            <tr key={item.productId || item.id || index}>
                                <td>{item.productId || item.id || 'N/A'}</td>
                                <td>{item.productName || item.name || 'N/A'}</td>
                                <td>{item.genericName || item.generic || 'N/A'}</td>
                                <td>{item.therapyArea || item.therapy || 'N/A'}</td>
                                <td>{item.indication || 'N/A'}</td>
                            </tr>
                        ))
                    ) : (
                        <tr>
                            <td colSpan="5" style={{ textAlign: 'center', padding: '20px' }}>
                                No data available
                            </td>
                        </tr>
                    )}
                </tbody>
            </table>

            <div style={{ marginTop: "20px", textAlign: 'center' }}>
                <button
                    onClick={() => setPage((p) => Math.max(p - 1, 1))}
                    disabled={page === 1}
                    style={{ marginRight: '10px' }}
                >
                    Previous
                </button>
                <span style={{ margin: "0 15px", fontWeight: 'bold' }}>
                    Page {page}
                </span>
                <button
                    onClick={() => setPage((p) => p + 1)}
                    disabled={data.length < pageSize}
                >
                    Next
                </button>
            </div>
        </div>
    );
}