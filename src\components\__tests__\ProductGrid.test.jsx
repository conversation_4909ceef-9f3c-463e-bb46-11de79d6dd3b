import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import axios from 'axios';
import ProductGrid from '../ProductGrid';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

describe('ProductGrid', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it('renders loading state initially', () => {
        // Mock axios to never resolve
        mockedAxios.get.mockImplementation(() => new Promise(() => {}));
        
        render(<ProductGrid />);
        
        expect(screen.getByText('Loading products...')).toBeInTheDocument();
        expect(screen.getByText('⏳')).toBeInTheDocument();
    });

    it('renders product data when API call succeeds', async () => {
        const mockData = [
            {
                productId: 1,
                productName: 'Test Product 1',
                genericName: 'Generic 1',
                therapyArea: 'Cardiology',
                indication: 'Hypertension'
            },
            {
                productId: 2,
                productName: 'Test Product 2',
                genericName: 'Generic 2',
                therapyArea: 'Oncology',
                indication: 'Cancer'
            }
        ];

        mockedAxios.get.mockResolvedValueOnce({
            data: mockData
        });

        render(<ProductGrid />);

        await waitFor(() => {
            expect(screen.getByText('Test Product 1')).toBeInTheDocument();
            expect(screen.getByText('Test Product 2')).toBeInTheDocument();
            expect(screen.getByText('Generic 1')).toBeInTheDocument();
            expect(screen.getByText('Cardiology')).toBeInTheDocument();
        });

        expect(screen.getByText('Total items: 2')).toBeInTheDocument();
    });

    it('renders error state when API call fails', async () => {
        mockedAxios.get.mockRejectedValueOnce(new Error('Network Error'));

        render(<ProductGrid />);

        await waitFor(() => {
            expect(screen.getByText('⚠️ Error loading data')).toBeInTheDocument();
            expect(screen.getByText(/Request error: Network Error/)).toBeInTheDocument();
        });

        expect(screen.getByText('Retry')).toBeInTheDocument();
        expect(screen.getByText('Use Test Data')).toBeInTheDocument();
    });

    it('uses test data when checkbox is checked', async () => {
        render(<ProductGrid />);

        // Find and click the test data checkbox
        const testDataCheckbox = screen.getByLabelText(/Use Test Data/);
        testDataCheckbox.click();

        await waitFor(() => {
            expect(screen.getByText('Test Product 1')).toBeInTheDocument();
            expect(screen.getByText('Test Product 2')).toBeInTheDocument();
            expect(screen.getByText('Test Product 3')).toBeInTheDocument();
        });

        expect(screen.getByText('Total items: 3')).toBeInTheDocument();
    });
});
