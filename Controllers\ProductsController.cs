﻿using Microsoft.AspNetCore.Mvc;
using ReactApp_JS.Server;
using System.Text;
using System.Text.Json;

[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private readonly List<Product> _products;

    public ProductsController()
    {
        _products = Enumerable.Range(1, 100).Select(i => new Product
        {
            ProductId = i,
            ProductName = $"Product {i}",
            GenericName = $"Generic {i % 10}",
            TherapyArea = $"Therapy {i % 5}",
            Indication = $"Indication {i % 3}"
        }).ToList();
    }

    [HttpGet]
    public IActionResult Get([FromQuery] string? search, string? sortBy, int page = 1, int pageSize = 10)
    {
        var query = _products.AsQueryable();
        if (!string.IsNullOrEmpty(search))
            query = query.Where(p =>
                p.ProductName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                p.GenericName.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                p.TherapyArea.Contains(search, StringComparison.OrdinalIgnoreCase) ||
                p.Indication.Contains(search, StringComparison.OrdinalIgnoreCase));

        if (!string.IsNullOrEmpty(sortBy))
        {
            var prop = typeof(Product).GetProperty(sortBy);
            if (prop != null)
                query = query.OrderBy(p => prop.GetValue(p));
        }

        var paged = query.Skip((page - 1) * pageSize).Take(pageSize).ToList();
        return Ok(paged);
    }

    [HttpGet("export")]
    public IActionResult Export()
    {
        var csv = new StringBuilder();
        csv.AppendLine("ProductId,ProductName,GenericName,TherapyArea,Indication");

        foreach (var p in _products)
            csv.AppendLine($"{p.ProductId},{p.ProductName},{p.GenericName},{p.TherapyArea},{p.Indication}");

        return File(Encoding.UTF8.GetBytes(csv.ToString()), "text/csv", "products.csv");
    }

    [HttpGet("advanced-export")]
    public IActionResult AdvancedExport()
    {
        var json = JsonSerializer.Serialize(_products);
        return File(Encoding.UTF8.GetBytes(json), "application/json", "products.json");
    }
}
