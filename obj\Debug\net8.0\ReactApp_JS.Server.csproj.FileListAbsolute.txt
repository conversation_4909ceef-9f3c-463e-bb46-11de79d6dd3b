C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\spa.proxy.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\appsettings.Development.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\ReactApp_JS.Server.exe
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\ReactApp_JS.Server.deps.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\ReactApp_JS.Server.runtimeconfig.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\ReactApp_JS.Server.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\ReactApp_JS.Server.pdb
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\Microsoft.AspNetCore.SpaProxy.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\spa.proxy.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.AssemblyInfo.cs
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets\msbuild.ReactApp_JS.Server.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets\msbuild.build.ReactApp_JS.Server.props
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.ReactApp_JS.Server.props
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.ReactApp_JS.Server.props
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\staticwebassets.pack.json
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\scopedcss\bundle\ReactApp_JS.Server.styles.css
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.csproj.CopyComplete
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\refint\ReactApp_JS.Server.dll
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.pdb
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ReactApp_JS.Server.genruntimeconfig.cache
C:\Users\<USER>\source\repos\ReactApp_JS\ReactApp_JS.Server\obj\Debug\net8.0\ref\ReactApp_JS.Server.dll
